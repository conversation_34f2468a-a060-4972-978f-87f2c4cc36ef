"""
测试efinance API的指数数据获取
"""
import efinance as ef
import pandas as pd

def test_index_quotes():
    """测试指数行情获取"""
    print("=== 测试指数行情获取 ===")

    # 测试不同的指数代码格式
    test_codes = [
        ['000300'],  # 沪深300
        ['000905'],  # 中证500
        ['000852'],  # 中证1000
        ['000016'],  # 上证50
        ['sh000300'],  # 带sh前缀
        ['sz399001'],  # 深证成指
    ]

    for codes in test_codes:
        try:
            print(f"\n测试代码: {codes}")
            df = ef.stock.get_realtime_quotes(codes)
            print(f"成功获取 {len(df)} 条数据")
            if not df.empty:
                print(df[['代码', '名称', '最新价']].head())
        except Exception as e:
            print(f"失败: {e}")

    # 测试获取所有股票实时行情（包含指数）
    print("\n=== 测试获取所有实时行情 ===")
    try:
        all_quotes = ef.stock.get_realtime_quotes()
        print(f"获取到 {len(all_quotes)} 条实时行情")
        print(f"列名: {all_quotes.columns.tolist()}")

        # 查看前几行数据
        print("\n前5行数据:")
        print(all_quotes.head())

        # 查找指数相关的行情（使用正确的列名）
        name_col = None
        for col in ['名称', 'name', '股票名称', '证券名称']:
            if col in all_quotes.columns:
                name_col = col
                break

        if name_col:
            index_quotes = all_quotes[all_quotes[name_col].str.contains('300|500|1000|50|指数', na=False)]
            if not index_quotes.empty:
                print(f"找到 {len(index_quotes)} 个指数相关行情:")
                code_col = '代码' if '代码' in all_quotes.columns else all_quotes.columns[0]
                price_col = '最新价' if '最新价' in all_quotes.columns else all_quotes.columns[2]
                print(index_quotes[[code_col, name_col, price_col]].head(10))
            else:
                print("未找到指数相关行情")
        else:
            print("未找到名称列")

    except Exception as e:
        print(f"获取所有实时行情失败: {e}")

def test_futures_quotes():
    """测试期货行情获取"""
    print("\n=== 测试期货行情获取 ===")

    try:
        df = ef.futures.get_realtime_quotes()
        print(f"获取到 {len(df)} 个期货合约")

        # 查看市场类型
        print("\n市场类型分布:")
        print(df['市场类型'].value_counts())

        # 查找中金所的合约
        cffex_contracts = df[df['市场类型'] == '中金所']
        print(f"\n中金所合约数量: {len(cffex_contracts)}")

        if not cffex_contracts.empty:
            print("中金所合约:")
            print(cffex_contracts[['期货代码', '期货名称', '最新价', '成交量']].head(20))

            # 查找股指期货
            stock_index_futures = cffex_contracts[cffex_contracts['期货代码'].str.contains('IC|IM|IF|IH', na=False)]
            if not stock_index_futures.empty:
                print(f"\n股指期货 ({len(stock_index_futures)} 个):")
                print(stock_index_futures[['期货代码', '期货名称', '最新价', '成交量']])
            else:
                print("\n未找到IC|IM|IF|IH格式的股指期货")
                # 查看所有中金所合约的代码模式
                print("所有中金所期货代码:")
                print(cffex_contracts['期货代码'].unique())

        # 查找名称中包含股指的
        stock_index_by_name = df[df['期货名称'].str.contains('股指|沪深|中证|上证|50|300|500|1000', na=False)]
        if not stock_index_by_name.empty:
            print(f"\n按名称筛选的股指期货 ({len(stock_index_by_name)} 个):")
            print(stock_index_by_name[['期货代码', '期货名称', '最新价', '成交量']].head(10))
        else:
            print("\n未找到股指期货相关合约")

    except Exception as e:
        print(f"获取期货行情失败: {e}")

def test_futures_base_info():
    """测试期货基础信息"""
    print("\n=== 测试期货基础信息 ===")

    try:
        df = ef.futures.get_futures_base_info()
        print(f"获取到 {len(df)} 个期货基础信息")
        print(f"列名: {df.columns.tolist()}")

        # 查找中金所
        cffex_info = df[df['市场类型'] == '中金所']
        print(f"\n中金所期货数量: {len(cffex_info)}")

        if not cffex_info.empty:
            print("中金所期货:")
            print(cffex_info.head(20))

            # 查找股指期货相关的
            print("\n查找股指期货相关合约:")
            for pattern in ['IC', 'IM', 'IF', 'IH']:
                matches = cffex_info[cffex_info['期货代码'].str.contains(pattern, na=False)]
                if not matches.empty:
                    print(f"\n包含 {pattern} 的合约:")
                    print(matches[['期货代码', '期货名称', '行情ID']])

    except Exception as e:
        print(f"获取期货基础信息失败: {e}")

def test_specific_index_quotes():
    """测试特定指数行情获取"""
    print("\n=== 测试特定指数行情获取 ===")

    # 尝试使用股票历史数据接口获取指数
    index_codes = ['000300', '000905', '000852', '000016']

    for code in index_codes:
        try:
            print(f"\n测试指数 {code}:")
            # 尝试获取历史数据
            df = ef.stock.get_quote_history(code, beg='20241201', end='20241231')
            if not df.empty:
                print(f"成功获取历史数据，最新价格: {df.iloc[-1]['收盘']}")
                print(df.tail(1)[['股票名称', '日期', '收盘']])
            else:
                print("未获取到数据")
        except Exception as e:
            print(f"失败: {e}")

if __name__ == "__main__":
    # test_index_quotes()
    # test_futures_quotes()
    # test_futures_base_info()
    # test_specific_index_quotes()


    # df = ef.futures.get_realtime_quotes(['225.ps2603'])
    # print(df.shape)
    # print(df.columns)
    # print(df.head())
    
    df = ef.futures.get_futures_base_info()
    print(df.shape)
    print(df.columns)
    print(df.head())


    # df = ef.stock.get_quote_history('000300')
    # print(df.shape)
    # print(df.columns)
    # print(df.head())

    # df = ef.stock.get_base_info(['000300', '000852', '000905', '000016'])
    # print(df.shape)
    # print(df.columns)
    # print(df.head())
    
    # df = ef.stock.get_quote_history('000852', beg='20241201', end='20241210')
    # print(df.shape)
    # print(df.columns)
    # print(df.head())
    
    # df = ef.stock.get_realtime_quotes('沪深系列指数')
    # print(df.shape)
    # print(df.columns)
    # print(df.head())
    # # for index in ['000300', '000905', '000852', '000016']:
    # #     print(df[df['股票代码'] == index])
        
    # for name in ['沪深300', '中证500', '中证1000', '上证50']:
    #     print(df[df['股票名称'] == name])

