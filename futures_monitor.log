2025-07-07 10:33:52,059 - src.data_providers.index_provider - WARNING - adata模块不可用，仅使用efinance数据源
2025-07-07 10:33:52,060 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 10:33:52,060 - __main__ - INFO - 验证数据源可用性...
2025-07-07 10:33:52,061 - src.data_providers.index_provider - ERROR - efinance获取指数行情失败: "指定的行情参数 `['sh000300']` 不正确"
2025-07-07 10:33:52,061 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': False}
2025-07-07 10:33:52,062 - __main__ - ERROR - 系统运行错误: 所有数据源都不可用
2025-07-07 10:33:52,062 - __main__ - INFO - 正在停止监控系统...
2025-07-07 10:33:52,062 - __main__ - INFO - 监控系统已停止
2025-07-07 11:26:08,900 - src.data_providers.index_provider - WARNING - adata模块不可用，仅使用efinance数据源
2025-07-07 11:26:08,900 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 11:26:08,901 - __main__ - INFO - 验证数据源可用性...
2025-07-07 11:26:09,176 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 11:26:09,176 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': False}
2025-07-07 11:26:09,177 - __main__ - ERROR - 系统运行错误: 所有数据源都不可用
2025-07-07 11:26:09,177 - __main__ - INFO - 正在停止监控系统...
2025-07-07 11:26:09,177 - __main__ - INFO - 监控系统已停止
2025-07-07 12:00:54,459 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:00:54,460 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:00:54,460 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:00:54,777 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:00:54,815 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:00:54,815 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:00:54,815 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:00:54
2025-07-07 12:00:55,015 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 12:00:55,343 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:00:55,343 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:00:55,642 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:00:55,782 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:00:55,783 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:00:55,870 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:00:55,871 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:00:55,871 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,873 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:00:55,873 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,874 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:00:55,876 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,877 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:00:55,877 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,878 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:00:55,879 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,890 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:00:55,891 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:00:55,893 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:00:55,894 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:00:55,918 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:01:55,908 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:01:55
2025-07-07 12:01:56,113 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:01:56,114 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:01:56,273 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:01:56,467 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:01:56,467 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:01:56,563 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:01:56,563 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:01:56,563 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,564 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:01:56,564 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,565 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:01:56,565 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,566 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:01:56,567 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,568 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:01:56,568 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,569 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:01:56,569 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:01:56,570 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:01:56,570 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:01:56,586 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:02:56,589 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:02:56
2025-07-07 12:02:56,841 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:02:56,842 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:02:57,018 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:02:57,189 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:02:57,189 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:02:57,287 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:02:57,288 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:02:57,288 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,289 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:02:57,289 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,290 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:02:57,290 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,291 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:02:57,292 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,293 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:02:57,293 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,294 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:02:57,294 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:02:57,295 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:02:57,296 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:02:57,311 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:03:57,312 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:03:57
2025-07-07 12:03:57,701 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:03:57,701 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:03:57,946 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:03:58,129 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:03:58,130 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:03:58,570 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:03:58,571 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:03:58,571 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,572 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:03:58,572 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,573 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:03:58,573 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,574 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:03:58,575 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,576 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:03:58,576 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,577 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:03:58,579 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:03:58,579 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:03:58,580 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:03:58,596 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:04:58,608 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:04:58
2025-07-07 12:04:58,812 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:04:58,812 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:04:58,939 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:04:59,220 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:04:59,220 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:04:59,311 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:04:59,312 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:04:59,312 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,313 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:04:59,314 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,314 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:04:59,314 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,315 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:04:59,316 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,317 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:04:59,317 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,318 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:04:59,318 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:04:59,319 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:04:59,321 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:04:59,336 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:05:59,347 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:05:59
2025-07-07 12:05:59,731 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:05:59,732 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:06:00,004 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:06:00,166 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:06:00,167 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:06:00,571 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:06:00,571 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:06:00,572 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,573 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:06:00,573 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,574 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:06:00,574 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,576 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:06:00,576 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,578 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:06:00,578 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,579 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:06:00,579 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:06:00,580 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:06:00,580 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:06:00,596 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:07:00,606 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:07:00
2025-07-07 12:07:00,999 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:07:00,999 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:07:01,253 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:07:01,392 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:07:01,392 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:07:01,805 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:07:01,805 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:07:01,805 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,807 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:07:01,808 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,808 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:07:01,809 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,810 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:07:01,810 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,811 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:07:01,811 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,812 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:07:01,813 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:07:01,814 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:07:01,814 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:07:01,830 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:08:01,834 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:08:01
2025-07-07 12:08:02,067 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:08:02,068 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:08:02,249 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:08:02,416 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:08:02,416 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:08:02,505 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:08:02,506 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:08:02,506 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,507 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:08:02,507 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,508 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:08:02,508 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,509 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:08:02,510 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,511 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:08:02,511 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,512 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:08:02,512 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:08:02,513 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:08:02,514 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:08:02,530 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:09:02,531 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:09:02
2025-07-07 12:09:02,923 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:09:02,924 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:09:03,190 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:09:03,357 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:09:03,357 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:09:03,771 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:09:03,772 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:09:03,772 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,773 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:09:03,773 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,774 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:09:03,774 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,775 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:09:03,775 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,777 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:09:03,777 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,779 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:09:03,779 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:09:03,780 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:09:03,780 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:09:03,796 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:10:03,798 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:10:03
2025-07-07 12:10:04,179 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:10:04,180 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:10:04,473 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:10:04,634 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:10:04,634 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:10:05,052 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:10:05,053 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:10:05,053 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,054 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:10:05,055 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,055 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:10:05,056 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,058 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:10:05,060 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,063 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:10:05,063 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,066 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:10:05,068 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:05,069 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:10:05,070 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:10:05,091 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:10:10,681 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:10:10,681 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:10:10,682 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:10:10,882 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:10:10,988 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:10:10,988 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:10:10,989 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:10:10
2025-07-07 12:10:11,335 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 12:10:11,607 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:10:11,607 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:10:11,863 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:10:11,976 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:10:11,976 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:10:12,381 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:10:12,381 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:10:12,381 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,385 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:10:12,385 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,387 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:10:12,387 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,390 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:10:12,390 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,391 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:10:12,392 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,393 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:10:12,393 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:10:12,394 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:10:12,395 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:10:12,438 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:11:12,453 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:11:12
2025-07-07 12:11:12,835 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:11:12,835 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:11:13,108 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:11:13,430 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:11:13,430 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:11:13,853 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:11:13,854 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:11:13,854 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,855 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:11:13,855 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,856 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:11:13,856 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,857 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:11:13,857 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,859 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:11:13,859 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,860 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:11:13,860 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:11:13,861 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:11:13,862 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:11:13,891 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:12:13,904 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:12:13
2025-07-07 12:12:14,279 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:12:14,279 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:12:14,527 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:12:14,727 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:12:14,728 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:12:15,148 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:12:15,148 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:12:15,148 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,149 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:12:15,150 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,150 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:12:15,151 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,153 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:12:15,153 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,155 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:12:15,155 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,156 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:12:15,157 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:12:15,158 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:12:15,159 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:12:15,188 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:13:15,195 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:13:15
2025-07-07 12:13:15,666 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:13:15,666 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:13:15,995 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:13:16,288 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:13:16,288 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:13:16,795 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:13:16,796 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:13:16,796 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,797 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:13:16,798 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,798 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:13:16,799 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,799 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:13:16,800 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,801 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:13:16,801 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,802 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:13:16,802 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:13:16,804 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:13:16,804 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:13:16,828 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:14:16,832 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:14:16
2025-07-07 12:14:17,197 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约
2025-07-07 12:14:17,197 - __main__ - INFO - 获取到 30 个期货合约
2025-07-07 12:14:17,450 - src.data_providers.efinance_provider - INFO - 获取到 30 个期货合约行情
2025-07-07 12:14:17,621 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:14:17,621 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 12:14:18,027 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 12:14:18,027 - src.calculators.basis_calculator - INFO - 成功计算 30 个合约的基差
2025-07-07 12:14:18,028 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,029 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 193 天
2025-07-07 12:14:18,030 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,032 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 44 天
2025-07-07 12:14:18,032 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,034 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 191 天
2025-07-07 12:14:18,035 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,037 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:14:18,038 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,039 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 201 天
2025-07-07 12:14:18,040 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 12:14:18,042 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 192 天
2025-07-07 12:14:18,042 - src.calculators.spread_calculator - INFO - 成功计算 26 个价差组合
2025-07-07 12:14:18,069 - __main__ - INFO - 等待 60 秒后下次更新...
2025-07-07 12:24:39,587 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:24:39,588 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:24:39,588 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:24:41,006 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:24:41,058 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:24:41,059 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:24:41,059 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:24:41
2025-07-07 12:24:41,923 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 12:24:43,536 - src.data_providers.efinance_provider - INFO - 获取到 994 个期货行情数据
2025-07-07 12:24:43,536 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 12:24:43,546 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 12:24:43,547 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: property 'contract_month' of 'FuturesContract' object has no setter
2025-07-07 12:24:43,550 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:25:13,563 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:25:13
2025-07-07 12:25:15,212 - src.data_providers.efinance_provider - INFO - 获取到 994 个期货行情数据
2025-07-07 12:25:15,213 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 12:25:15,215 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 12:25:15,216 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: property 'contract_month' of 'FuturesContract' object has no setter
2025-07-07 12:25:15,218 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:25:45,227 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:25:45
2025-07-07 12:25:45,401 - src.data_providers.efinance_provider - INFO - 获取到 994 个期货行情数据
2025-07-07 12:25:45,402 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 12:25:45,404 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 12:25:45,405 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: property 'contract_month' of 'FuturesContract' object has no setter
2025-07-07 12:25:45,407 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:26:05,101 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:26:05,102 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:26:05,102 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:26:05,447 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:26:05,487 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:26:05,488 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:26:05,488 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:26:05
2025-07-07 12:26:05,681 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 12:26:05,742 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,743 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,766 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,768 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,789 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,789 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,825 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,825 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,845 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,845 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:05,866 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:26:06,074 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:26:36,079 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:26:36
2025-07-07 12:26:36,200 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,302 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,398 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,515 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,643 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:26:36,743 - src.data_providers.efinance_provider - ERROR - 获取期货合约信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:26:36,753 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:37:36,021 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:37:36,022 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:37:36,022 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:37:36,284 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:37:36,349 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:37:36,349 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:37:36,350 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:37:36
2025-07-07 12:37:36,389 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,433 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,454 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,477 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,512 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:36,554 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:37:36,554 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:37:54,415 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 12:37:54,415 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 12:37:54,415 - __main__ - INFO - 验证数据源可用性...
2025-07-07 12:37:54,594 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 12:37:54,633 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 12:37:54,634 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 12:37:54,634 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:37:54
2025-07-07 12:37:54,680 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,708 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,730 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,754 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,777 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:37:54,800 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:37:54,800 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:38:24,813 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:38:24
2025-07-07 12:38:24,944 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,066 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,166 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,280 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,401 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:25,530 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:38:25,531 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:38:55,536 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:38:55
2025-07-07 12:38:55,645 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:55,749 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:55,854 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:55,948 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:56,065 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:38:56,162 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:38:56,162 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:39:26,175 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:39:26
2025-07-07 12:39:26,274 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,398 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,493 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,594 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,718 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:26,847 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:39:26,847 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:39:56,853 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:39:56
2025-07-07 12:39:56,978 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,092 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,190 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,299 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,405 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:39:57,525 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:39:57,525 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:40:27,536 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:40:27
2025-07-07 12:40:27,658 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,759 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,881 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,907 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,950 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:27,996 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:40:27,997 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:40:58,008 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:40:58
2025-07-07 12:40:58,135 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,241 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,343 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,447 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,664 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,700 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,701 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,704 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,705 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,721 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,721 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,722 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,722 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,722 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,763 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,801 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,801 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,809 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,818 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,818 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,820 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,824 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,825 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,852 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,859 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,898 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,904 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,916 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,928 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,929 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,929 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,929 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,932 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,964 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:58,977 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,007 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,010 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,018 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,028 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,028 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,029 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,030 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,030 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,070 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,082 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,110 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,111 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,120 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,123 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,128 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,130 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,132 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,136 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,201 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:40:59,299 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:40:59,299 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:41:29,302 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:41:29
2025-07-07 12:41:29,411 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,522 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,628 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,737 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,842 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:41:29,948 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:41:29,949 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:41:59,954 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:41:59
2025-07-07 12:42:00,083 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,205 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,308 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,410 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,506 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:00,626 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:42:00,626 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:42:30,633 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:42:30
2025-07-07 12:42:30,758 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:30,892 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:30,994 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:31,096 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:31,197 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:42:31,318 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:42:31,318 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 12:43:01,330 - __main__ - INFO - 开始数据更新 - 2025-07-07 12:43:01
2025-07-07 12:43:01,456 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:01,558 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:01,669 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:01,786 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:01,889 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 12:43:02,012 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 12:43:02,012 - __main__ - WARNING - 未获取到期货合约信息
2025-07-07 14:36:51,167 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 14:36:51,168 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 14:36:51,168 - __main__ - INFO - 验证数据源可用性...
2025-07-07 14:36:51,407 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 14:36:51,451 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 14:36:51,452 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 14:36:51,452 - __main__ - INFO - 开始数据更新 - 2025-07-07 14:36:51
2025-07-07 14:36:51,472 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,495 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,520 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,555 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,585 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:51,609 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:36:51,610 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-07 14:36:53,656 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,701 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,736 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,756 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,779 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:53,824 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:36:53,824 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-07 14:36:57,964 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,064 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,161 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,280 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,394 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:36:58,523 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:36:58,525 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-07 14:37:06,650 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:06,771 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:06,898 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:07,020 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:07,121 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:37:07,218 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:37:07,218 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:37:07,219 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-07 14:38:07,226 - __main__ - INFO - 开始数据更新 - 2025-07-07 14:38:07
2025-07-07 14:38:07,346 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,444 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,573 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,691 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,793 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:07,896 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:07,897 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-07 14:38:10,014 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,116 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,248 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,372 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,486 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:10,603 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:10,604 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-07 14:38:14,733 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:14,839 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:14,940 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:15,049 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:15,144 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:15,272 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:15,272 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-07 14:38:23,394 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,514 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,610 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,714 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,823 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:38:23,924 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:23,925 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:38:23,925 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-07 14:40:10,382 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 14:40:10,384 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 14:40:10,384 - __main__ - INFO - 验证数据源可用性...
2025-07-07 14:40:11,051 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 14:40:11,117 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 14:40:11,118 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 14:40:11,119 - __main__ - INFO - 开始数据更新 - 2025-07-07 14:40:11
2025-07-07 14:40:11,148 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,181 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,211 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,239 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,261 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:11,285 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:11,287 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-07 14:40:13,328 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,357 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,383 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,407 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,439 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:13,469 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:13,471 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-07 14:40:17,521 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,567 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,605 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,631 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,655 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:17,677 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:17,678 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-07 14:40:25,721 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,744 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,767 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,790 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,811 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 14:40:25,835 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:25,840 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 14:40:25,842 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-07 15:01:31,165 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 15:01:31,166 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 15:01:31,166 - __main__ - INFO - 验证数据源可用性...
2025-07-07 15:01:31,406 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 15:01:31,533 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 15:01:31,534 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 15:01:31,534 - __main__ - INFO - 开始数据更新 - 2025-07-07 15:01:31
2025-07-07 15:01:31,623 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:31,758 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:31,857 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:31,961 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:32,068 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:32,189 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:32,189 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-07 15:01:34,278 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,367 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,476 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,565 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,667 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:34,756 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:34,757 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-07 15:01:38,846 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:38,950 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:39,042 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:39,133 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:39,227 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:39,318 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:39,319 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-07 15:01:47,437 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,527 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,618 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,704 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,814 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-07 15:01:47,927 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:47,927 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-07 15:01:47,927 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-07 18:40:37,670 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 18:40:37,671 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 18:40:37,671 - __main__ - INFO - 验证数据源可用性...
2025-07-07 18:40:37,939 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 18:40:37,979 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 18:40:37,979 - __main__ - INFO - 数据源状态: {'efinance': False, 'adata': True}
2025-07-07 18:40:37,980 - __main__ - INFO - 开始数据更新 - 2025-07-07 18:40:37
2025-07-07 18:40:38,222 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 18:40:40,088 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-07 18:40:40,430 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-07 18:40:40,430 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-07 18:40:40,430 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 18:40:40,436 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 18:40:40,443 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-07 18:40:40,445 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-07 18:40:40,452 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-07 18:40:40,454 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-07 18:40:40,460 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-07 18:40:40,465 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-07 18:40:40,471 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-07 18:40:40,472 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-07 18:40:40,600 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-07 18:40:40,600 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-07 18:40:40,689 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-07 18:40:40,689 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,689 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 18:40:40,690 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,690 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 18:40:40,690 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,691 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 18:40:40,691 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,692 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 18:40:40,692 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,693 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 18:40:40,693 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,693 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 18:40:40,694 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,695 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 18:40:40,695 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,696 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 18:40:40,696 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,696 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 18:40:40,697 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,697 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 18:40:40,697 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,698 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 18:40:40,698 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,699 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 18:40:40,699 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,700 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 18:40:40,700 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,701 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 18:40:40,701 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,702 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 18:40:40,702 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,703 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 18:40:40,703 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-07 18:40:40,703 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,738 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 4604 天
2025-07-07 18:40:40,739 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,739 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 18:40:40,739 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,740 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 18:40:40,740 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,741 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 18:40:40,741 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,758 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 2479 天
2025-07-07 18:40:40,759 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,759 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 18:40:40,760 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,761 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 18:40:40,761 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,762 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 18:40:40,763 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,800 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 5083 天
2025-07-07 18:40:40,800 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,802 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 18:40:40,803 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,804 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 18:40:40,804 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,805 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 18:40:40,806 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,849 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 4372 天
2025-07-07 18:40:40,849 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,850 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 18:40:40,850 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,851 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 18:40:40,851 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 18:40:40,852 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 18:40:40,852 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-07 18:40:40,969 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-07 18:44:05,098 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 18:44:05,099 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 18:44:05,099 - __main__ - INFO - 验证数据源可用性...
2025-07-07 18:44:18,653 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-07 18:44:18,678 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 18:44:18,678 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-07 18:44:18,679 - __main__ - INFO - 开始数据更新 - 2025-07-07 18:44:18
2025-07-07 18:44:35,833 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 18:44:35,834 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-07 22:08:35,589 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-07 22:08:35,590 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-07 22:08:35,590 - __main__ - INFO - 验证数据源可用性...
2025-07-07 22:08:35,975 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-07 22:08:36,041 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-07 22:08:36,041 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-07 22:08:36,041 - __main__ - INFO - 开始数据更新 - 2025-07-07 22:08:36
2025-07-07 22:08:36,211 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-07 22:08:38,156 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-07 22:08:38,321 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-07 22:08:38,322 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-07 22:08:38,322 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-07 22:08:38,331 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-07 22:08:38,340 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-07 22:08:38,343 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-07 22:08:38,355 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-07 22:08:38,359 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-07 22:08:38,364 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-07 22:08:38,368 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-07 22:08:38,377 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-07 22:08:38,377 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-07 22:08:38,656 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-07 22:08:38,656 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,657 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 22:08:38,657 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,658 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 22:08:38,659 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,661 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 22:08:38,662 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,662 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 22:08:38,662 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,663 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 22:08:38,664 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,665 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 22:08:38,665 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,666 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 22:08:38,667 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,668 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 22:08:38,668 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,669 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 22:08:38,670 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,670 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 22:08:38,672 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,672 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 22:08:38,672 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,672 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 22:08:38,673 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,673 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 55 天
2025-07-07 22:08:38,673 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,675 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 10 天
2025-07-07 22:08:38,675 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,676 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 115 天
2025-07-07 22:08:38,676 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,676 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 30 天
2025-07-07 22:08:38,676 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-07 22:08:38,677 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,700 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 4604 天
2025-07-07 22:08:38,701 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,702 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 22:08:38,703 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,704 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 22:08:38,705 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,706 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 22:08:38,706 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,719 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 2479 天
2025-07-07 22:08:38,720 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,721 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 22:08:38,722 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,723 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 22:08:38,723 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,724 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 22:08:38,724 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,751 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 5083 天
2025-07-07 22:08:38,753 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,753 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 22:08:38,753 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,754 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 22:08:38,754 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,755 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 22:08:38,755 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,779 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 4372 天
2025-07-07 22:08:38,780 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,780 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 21 天
2025-07-07 22:08:38,780 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,780 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 26 天
2025-07-07 22:08:38,780 - src.utils.trading_calendar - ERROR - 获取交易日失败: module 'efinance' has no attribute 'trade_calendar'
2025-07-07 22:08:38,781 - src.utils.trading_calendar - WARNING - 使用估算方法计算交易日: 61 天
2025-07-07 22:08:38,781 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-07 22:08:38,850 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 09:44:13,334 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 09:44:13,346 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 09:44:13,347 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 09:44:13,347 - __main__ - INFO - 验证数据源可用性...
2025-07-08 09:44:13,843 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 09:44:13,901 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 09:44:13,902 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 09:44:13,902 - __main__ - INFO - 开始数据更新 - 2025-07-08 09:44:13
2025-07-08 09:44:14,076 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-08 09:44:16,008 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 09:44:16,161 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 09:44:16,162 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 09:44:16,162 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 09:44:16,168 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 09:44:16,177 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 09:44:16,183 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 09:44:16,191 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 09:44:16,198 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 09:44:16,204 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 09:44:16,207 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 09:44:16,218 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 09:44:16,218 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 09:44:16,502 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 09:44:16,752 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 09:44:18,465 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 09:44:18,537 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 09:46:24,741 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 09:46:24,741 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 09:46:24,741 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 09:46:24,741 - __main__ - INFO - 验证数据源可用性...
2025-07-08 09:46:25,288 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 09:46:25,318 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 09:46:25,319 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 09:46:25,319 - __main__ - INFO - 开始数据更新 - 2025-07-08 09:46:25
2025-07-08 09:46:25,490 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-08 09:46:27,426 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 09:46:27,543 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 09:46:27,544 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 09:46:27,544 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 09:46:27,551 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 09:46:27,561 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 09:46:27,564 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 09:46:27,575 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 09:46:27,577 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 09:46:27,583 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 09:46:27,589 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 09:46:27,595 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 09:46:27,595 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 09:46:27,829 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 09:46:28,057 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 09:46:29,150 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 09:46:29,213 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:08:01,899 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 10:08:01,899 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 10:08:01,901 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 10:08:01,901 - __main__ - INFO - 验证数据源可用性...
2025-07-08 10:08:02,213 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 10:08:02,240 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 10:08:02,240 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 10:08:02,240 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:08:02
2025-07-08 10:08:02,388 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-08 10:08:04,356 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:08:04,494 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:08:04,495 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:08:04,495 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:08:04,503 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:08:04,512 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:08:04,515 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:08:04,523 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:08:04,526 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:08:04,531 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:08:04,536 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:08:04,547 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:08:04,547 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:08:04,804 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:08:05,013 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:08:06,056 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:08:06,132 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:09:36,140 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:09:36
2025-07-08 10:09:36,141 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:09:36,524 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:09:36,524 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:09:36,524 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:09:36,531 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:09:36,537 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:09:36,540 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:09:36,547 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:09:36,552 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:09:36,558 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:09:36,562 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:09:36,573 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:09:36,573 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:09:36,985 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:09:37,189 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:09:38,205 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:09:38,260 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:11:08,268 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:11:08
2025-07-08 10:11:08,268 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:11:08,657 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:11:08,657 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:11:08,657 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:11:08,662 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:11:08,669 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:11:08,675 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:11:08,680 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:11:08,682 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:11:08,690 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:11:08,694 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:11:08,699 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:11:08,699 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:11:09,076 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:11:09,285 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:11:10,280 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:11:10,334 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:12:40,349 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:12:40
2025-07-08 10:12:40,349 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:12:40,533 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:12:40,533 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:12:40,533 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:12:40,538 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:12:40,548 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:12:40,553 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:12:40,558 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:12:40,560 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:12:40,570 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:12:40,572 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:12:40,577 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:12:40,578 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:12:40,877 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:12:41,101 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:12:42,229 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:12:42,280 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:14:12,289 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:14:12
2025-07-08 10:14:12,290 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:14:12,697 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:14:12,697 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:14:12,698 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:14:12,702 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:14:12,714 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:14:12,716 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:14:12,723 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:14:12,727 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:14:12,732 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:14:12,734 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:14:12,743 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:14:12,743 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:14:13,142 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:14:13,334 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:14:14,335 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:14:14,381 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:30:00,056 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 10:30:00,056 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 10:30:00,057 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 10:30:00,057 - __main__ - INFO - 验证数据源可用性...
2025-07-08 10:30:00,468 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 10:30:00,492 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 10:30:00,492 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 10:30:00,493 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:30:00
2025-07-08 10:30:00,706 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-08 10:30:02,596 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:30:02,758 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:30:02,758 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:30:02,759 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:30:02,769 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:30:02,781 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:30:02,784 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:30:02,794 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:30:02,796 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:30:02,802 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:30:02,806 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:30:02,814 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:30:02,814 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:30:03,153 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:30:03,369 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:30:04,402 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:30:04,465 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:31:34,470 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:31:34
2025-07-08 10:31:34,470 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:31:34,736 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:31:34,736 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:31:34,736 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:31:34,739 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:31:34,744 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:31:34,748 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:31:34,753 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:31:34,756 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:31:34,761 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:31:34,764 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:31:34,770 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:31:34,771 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:31:35,073 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:31:35,306 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:31:36,384 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:31:36,437 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:33:06,448 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:33:06
2025-07-08 10:33:06,448 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:33:06,774 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:33:06,775 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:33:06,775 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:33:06,778 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:33:06,783 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:33:06,785 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:33:06,790 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:33:06,792 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:33:06,797 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:33:06,798 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:33:06,803 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:33:06,803 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:33:07,068 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:33:07,278 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:33:08,284 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:33:08,323 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:34:38,324 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:34:38
2025-07-08 10:34:38,325 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:34:38,500 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:34:38,500 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:34:38,501 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:34:38,503 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:34:38,512 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:34:38,516 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:34:38,527 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:34:38,532 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:34:38,542 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:34:38,546 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:34:38,557 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:34:38,557 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:34:38,870 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:34:39,088 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:34:40,110 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:34:40,150 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:36:10,153 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:36:10
2025-07-08 10:36:10,154 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:36:10,369 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:36:10,369 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:36:10,370 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:36:10,372 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:36:10,378 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:36:10,380 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:36:10,384 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:36:10,386 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:36:10,391 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:36:10,394 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:36:10,398 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:36:10,399 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:36:10,697 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:36:10,898 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:36:11,911 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:36:11,953 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:37:41,968 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:37:41
2025-07-08 10:37:41,969 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:37:42,202 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:37:42,202 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:37:42,203 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:37:42,205 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:37:42,210 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:37:42,212 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:37:42,218 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:37:42,220 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:37:42,225 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:37:42,227 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:37:42,232 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:37:42,233 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:37:42,613 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:37:42,817 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:37:43,884 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:37:43,929 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:39:13,934 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:39:13
2025-07-08 10:39:13,935 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:39:14,200 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:39:14,200 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:39:14,200 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:39:14,203 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:39:14,208 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:39:14,211 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:39:14,218 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:39:14,223 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:39:14,229 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:39:14,232 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:39:14,237 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:39:14,237 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:39:14,515 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:39:14,723 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:39:15,763 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:39:15,808 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:40:45,814 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:40:45
2025-07-08 10:40:45,814 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:40:46,216 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:40:46,216 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:40:46,216 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:40:46,223 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:40:46,235 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:40:46,240 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:40:46,253 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:40:46,258 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:40:46,271 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:40:46,276 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:40:46,288 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:40:46,289 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:40:46,696 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:40:47,008 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:40:48,642 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:40:48,695 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:42:18,708 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:42:18
2025-07-08 10:42:18,709 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:42:19,191 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:42:19,191 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:42:19,191 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:42:19,195 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:42:19,203 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:42:19,206 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:42:19,214 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:42:19,218 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:42:19,226 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:42:19,230 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:42:19,237 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:42:19,238 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:42:19,909 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:42:20,263 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:42:21,971 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:42:22,042 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:43:37,581 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 10:43:37,581 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 10:43:37,582 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 10:43:37,582 - __main__ - INFO - 验证数据源可用性...
2025-07-08 10:43:38,214 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 10:43:38,242 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 10:43:38,242 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 10:43:38,243 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:43:38
2025-07-08 10:43:51,281 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-08 10:43:51,282 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:43:52,044 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:43:52
2025-07-08 10:43:52,044 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:43:53,704 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:43:53,704 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:43:53,705 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:43:53,708 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:43:53,720 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:43:53,724 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:43:53,735 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:43:53,738 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:43:53,748 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:43:53,751 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:43:53,761 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:43:53,761 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:43:56,684 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:43:56,684 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:43:56,684 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:43:56,687 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:43:56,694 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:43:56,696 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:43:56,702 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:43:56,705 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:43:56,710 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:43:56,713 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:43:56,718 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:43:56,718 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:44:23,638 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:44:23,836 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:44:24,965 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:44:25,010 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:45:55,020 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:45:55
2025-07-08 10:45:55,021 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:45:55,228 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:45:55,229 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:45:55,229 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:45:55,232 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:45:55,238 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:45:55,241 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:45:55,247 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:45:55,251 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:45:55,257 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:45:55,260 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:45:55,266 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:45:55,267 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:45:55,555 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:45:55,778 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:45:57,447 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:45:57,507 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:47:27,515 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:47:27
2025-07-08 10:47:27,516 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:47:27,737 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:47:27,737 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:47:27,738 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:47:27,740 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:47:27,745 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:47:27,748 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:47:27,756 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:47:27,760 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:47:27,773 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:47:27,777 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:47:27,788 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:47:27,789 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:47:28,111 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:47:28,328 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:47:29,369 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:47:29,409 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:48:59,420 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:48:59
2025-07-08 10:48:59,420 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 10:48:59,609 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 10:48:59,609 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 10:48:59,609 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 10:48:59,612 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 10:48:59,617 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 10:48:59,619 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 10:48:59,624 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 10:48:59,627 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 10:48:59,631 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 10:48:59,633 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 10:48:59,638 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 10:48:59,638 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 10:48:59,873 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 10:49:00,086 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 10:49:01,104 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 10:49:01,141 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 10:50:14,716 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 10:50:14,716 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 10:50:14,716 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 10:50:14,716 - __main__ - INFO - 验证数据源可用性...
2025-07-08 10:50:15,013 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 10:50:15,043 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 10:50:15,043 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 10:50:15,043 - __main__ - INFO - 开始数据更新 - 2025-07-08 10:50:15
2025-07-08 10:50:15,087 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,089 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,091 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,092 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,094 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,097 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,098 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,098 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,098 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,110 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,113 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,117 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,117 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,117 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,126 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,130 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,135 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,135 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,139 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,145 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,145 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,151 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,155 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,160 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,160 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,160 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,162 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,166 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,173 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,175 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,176 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,179 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,181 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,181 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=5&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,182 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,185 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,188 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,196 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,196 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=8&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,197 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,199 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=9&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,204 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=3&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,204 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=4&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,206 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=7&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,211 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=10&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:15,231 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=2&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 10:50:15,231 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-08 10:50:17,263 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:17,286 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:17,309 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:17,329 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:17,347 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:17,367 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 10:50:17,367 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-08 10:50:21,423 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:21,455 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:21,480 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:21,505 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:21,528 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:21,553 - src.data_providers.efinance_provider - WARNING - 获取期货基础信息 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 10:50:21,553 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-08 10:50:29,608 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:29,654 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:29,686 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:29,731 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:29,751 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 10:50:29,794 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 10:50:29,794 - src.data_providers.efinance_provider - ERROR - 获取期货基础信息最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 10:50:29,795 - __main__ - WARNING - 未获取到期货合约信息或行情数据
2025-07-08 11:09:29,807 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 11:09:29,807 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 11:09:29,807 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 11:09:29,808 - __main__ - INFO - 验证数据源可用性...
2025-07-08 11:09:30,715 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 11:09:30,826 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 11:09:30,826 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 11:09:30,827 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:09:30
2025-07-08 11:09:31,296 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-08 11:09:32,936 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:09:33,288 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:09:33,288 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:09:33,288 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:09:33,294 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:09:33,317 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:09:33,321 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:09:33,351 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:09:33,357 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:09:33,388 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:09:33,401 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:09:33,437 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:09:33,438 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:09:34,149 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:09:35,465 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:09:35,723 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:09:35,857 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:16:15,917 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 11:16:15,918 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 11:16:15,918 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 11:16:15,918 - __main__ - INFO - 验证数据源可用性...
2025-07-08 11:16:16,416 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 11:16:16,529 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 11:16:16,529 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 11:16:16,529 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:16:16
2025-07-08 11:16:16,805 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-08 11:16:18,635 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:16:18,918 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:16:18,918 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:16:18,919 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:16:18,924 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:16:18,950 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:16:18,953 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:16:18,969 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:16:18,971 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:16:18,987 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:16:18,990 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:16:19,012 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:16:19,013 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:16:19,411 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:16:19,954 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:16:20,123 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:16:20,191 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:17:50,194 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:17:50
2025-07-08 11:17:50,194 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:17:50,379 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:17:50,380 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:17:50,380 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:17:50,382 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:17:50,394 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:17:50,397 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:17:50,409 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:17:50,412 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:17:50,423 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:17:50,426 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:17:50,443 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:17:50,443 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:17:50,743 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:17:51,275 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:17:51,431 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:17:51,482 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:19:21,487 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:19:21
2025-07-08 11:19:21,488 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:19:21,866 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:19:21,866 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:19:21,867 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:19:21,869 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:19:21,878 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:19:21,881 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:19:21,890 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:19:21,893 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:19:21,903 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:19:21,906 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:19:21,916 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:19:21,916 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:19:22,339 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:19:22,862 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:19:23,020 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:19:23,064 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:20:53,075 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:20:53
2025-07-08 11:20:53,075 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:20:53,439 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:20:53,440 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:20:53,440 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:20:53,442 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:20:53,457 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:20:53,459 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:20:53,470 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:20:53,473 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:20:53,483 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:20:53,486 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:20:53,496 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:20:53,496 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:20:53,914 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:20:54,461 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:20:54,619 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:20:54,667 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:22:24,674 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:22:24
2025-07-08 11:22:24,676 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:22:25,047 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:22:25,048 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:22:25,048 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:22:25,050 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:22:25,062 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:22:25,064 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:22:25,074 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:22:25,076 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:22:25,086 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:22:25,088 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:22:25,098 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:22:25,099 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:22:25,544 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:22:26,078 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:22:26,240 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:22:26,293 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:23:56,298 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:23:56
2025-07-08 11:23:56,298 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:23:56,692 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:23:56,692 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:23:56,692 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:23:56,695 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:23:56,706 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:23:56,708 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:23:56,720 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:23:56,722 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:23:56,733 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:23:56,735 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:23:56,745 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:23:56,745 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:23:57,350 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:23:57,892 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:23:58,069 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:23:58,116 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:25:28,126 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:25:28
2025-07-08 11:25:28,126 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:25:28,508 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:25:28,509 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:25:28,509 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:25:28,511 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:25:28,522 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:25:28,525 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:25:28,535 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:25:28,538 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:25:28,548 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:25:28,553 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:25:28,566 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:25:28,567 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:25:28,972 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:25:29,482 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:25:29,636 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:25:29,674 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:26:59,688 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:26:59
2025-07-08 11:26:59,688 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:26:59,997 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:26:59,997 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:26:59,997 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:27:00,000 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:27:00,010 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:27:00,012 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:27:00,026 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:27:00,029 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:27:00,039 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:27:00,041 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:27:00,051 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:27:00,052 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:27:00,301 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:27:00,815 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:27:00,968 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:27:01,019 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:28:31,022 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:28:31
2025-07-08 11:28:31,022 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:28:31,209 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:28:31,210 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:28:31,210 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:28:31,213 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:28:31,223 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:28:31,226 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:28:31,237 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:28:31,239 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:28:31,250 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:28:31,253 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:28:31,263 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:28:31,263 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:28:31,567 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:28:32,105 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:28:32,262 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:28:32,302 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:30:02,303 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:30:02
2025-07-08 11:30:02,303 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:30:02,678 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:30:02,678 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:30:02,678 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:30:02,681 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:30:02,691 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:30:02,694 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:30:02,704 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:30:02,707 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:30:02,717 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:30:02,719 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:30:02,730 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:30:02,730 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:30:03,151 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:30:03,773 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:30:03,943 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:30:03,987 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:31:34,001 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:31:34
2025-07-08 11:31:34,001 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:31:34,243 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:31:34,243 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:31:34,243 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:31:34,246 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:31:34,257 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:31:34,259 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:31:34,270 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:31:34,273 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:31:34,284 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:31:34,286 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:31:34,296 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:31:34,296 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:31:34,548 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:31:35,058 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:31:35,210 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:31:35,252 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:33:05,265 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:33:05
2025-07-08 11:33:05,266 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:33:05,639 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:33:05,639 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:33:05,639 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:33:05,642 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:33:05,652 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:33:05,654 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:33:05,665 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:33:05,667 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:33:05,678 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:33:05,681 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:33:05,691 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:33:05,692 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:33:06,093 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:33:06,622 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:33:06,826 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:33:06,888 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:34:36,897 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:34:36
2025-07-08 11:34:36,898 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:34:37,156 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:34:37,156 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:34:37,156 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:34:37,160 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:34:37,171 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:34:37,173 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:34:37,184 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:34:37,186 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:34:37,196 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:34:37,200 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:34:37,210 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:34:37,210 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:34:37,476 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:34:37,992 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:34:38,146 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:34:38,189 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:36:08,193 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:36:08
2025-07-08 11:36:08,193 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:36:08,456 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:36:08,457 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:36:08,457 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:36:08,459 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:36:08,469 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:36:08,473 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:36:08,483 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:36:08,485 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:36:08,497 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:36:08,499 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:36:08,510 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:36:08,510 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:36:08,802 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:36:09,331 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:36:09,498 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:36:09,543 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:37:39,558 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:37:39
2025-07-08 11:37:39,558 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:37:39,844 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:37:39,844 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:37:39,844 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:37:39,850 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:37:39,865 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:37:39,870 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:37:39,888 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:37:39,891 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:37:39,909 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:37:39,914 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:37:39,935 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:37:39,936 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:37:40,334 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:37:41,053 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:37:41,262 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:37:41,355 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:39:11,370 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:39:11
2025-07-08 11:39:11,371 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:39:11,573 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:39:11,573 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:39:11,573 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:39:11,576 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:39:11,586 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:39:11,588 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:39:11,598 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:39:11,600 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:39:11,611 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:39:11,614 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:39:11,623 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:39:11,624 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:39:11,906 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:39:12,412 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:39:12,560 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:39:12,600 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:40:42,607 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:40:42
2025-07-08 11:40:42,608 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:40:42,804 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:40:42,804 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:40:42,804 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:40:42,807 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:40:42,818 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:40:42,820 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:40:42,831 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:40:42,833 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:40:42,844 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:40:42,846 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:40:42,857 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:40:42,857 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:40:43,104 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:40:43,617 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:40:43,769 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:40:43,810 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:42:13,813 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:42:13
2025-07-08 11:42:13,813 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:42:13,994 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:42:13,994 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:42:13,994 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:42:13,997 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:42:14,007 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:42:14,010 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:42:14,020 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:42:14,023 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:42:14,033 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:42:14,035 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:42:14,046 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:42:14,046 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:42:14,318 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:42:14,844 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:42:14,992 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:42:15,035 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:43:45,050 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:43:45
2025-07-08 11:43:45,050 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:43:45,382 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:43:45,382 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:43:45,382 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:43:45,385 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:43:45,398 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:43:45,402 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:43:45,415 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:43:45,419 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:43:45,430 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:43:45,433 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:43:45,443 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:43:45,444 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:43:45,741 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:43:46,255 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:43:46,407 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:43:46,449 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:45:16,455 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:45:16
2025-07-08 11:45:16,456 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:45:16,668 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:45:16,668 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:45:16,669 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:45:16,671 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:45:16,682 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:45:16,684 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:45:16,694 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:45:16,696 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:45:16,705 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:45:16,709 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:45:16,718 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:45:16,718 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:45:16,972 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:45:17,472 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:45:17,622 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:45:17,662 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:46:47,671 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:46:47
2025-07-08 11:46:47,671 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:46:48,075 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:46:48,075 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:46:48,075 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:46:48,078 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:46:48,087 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:46:48,090 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:46:48,099 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:46:48,102 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:46:48,112 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:46:48,114 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:46:48,125 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:46:48,125 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:46:48,540 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:46:49,033 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:46:49,180 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:46:49,242 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:48:19,246 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:48:19
2025-07-08 11:48:19,247 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:48:19,923 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:48:19,923 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:48:19,924 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:48:19,927 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:48:19,937 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:48:19,939 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:48:19,949 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:48:19,952 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:48:19,968 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:48:19,970 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:48:19,982 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:48:19,982 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:48:20,391 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:48:20,897 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:48:21,050 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:48:21,090 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:49:51,101 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:49:51
2025-07-08 11:49:51,102 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:49:51,519 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:49:51,519 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:49:51,520 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:49:51,525 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:49:51,549 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:49:51,554 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:49:51,579 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:49:51,584 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:49:51,609 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:49:51,614 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:49:51,639 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:49:51,639 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:49:52,301 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:49:53,496 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:49:53,854 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:49:53,964 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:51:23,978 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:51:23
2025-07-08 11:51:23,979 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:51:24,626 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:51:24,627 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:51:24,627 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:51:24,636 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:51:24,675 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:51:24,683 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:51:24,721 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:51:24,729 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:51:24,766 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:51:24,774 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:51:24,808 - src.data_providers.efinance_provider - INFO - 总共获取到 40 个期货合约和 40 个行情
2025-07-08 11:51:24,809 - __main__ - INFO - 获取到 40 个期货合约和 40 个行情
2025-07-08 11:51:25,719 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:51:28,538 - src.calculators.basis_calculator - INFO - 成功计算 40 个合约的基差
2025-07-08 11:51:29,532 - src.calculators.spread_calculator - INFO - 成功计算 36 个价差组合
2025-07-08 11:51:29,749 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:51:57,076 - src.utils.trading_calendar - INFO - adata模块可用，将用于获取交易日历
2025-07-08 11:51:57,077 - src.data_providers.index_provider - INFO - adata模块可用，将作为备用数据源
2025-07-08 11:51:57,077 - __main__ - INFO - === 金融股指期货监控系统启动 ===
2025-07-08 11:51:57,077 - __main__ - INFO - 验证数据源可用性...
2025-07-08 11:51:57,630 - src.data_providers.index_provider - INFO - efinance获取到 1 个指数行情
2025-07-08 11:51:57,755 - src.data_providers.index_provider - INFO - adata获取到 1 个指数行情
2025-07-08 11:51:57,755 - __main__ - INFO - 数据源状态: {'efinance': True, 'adata': True}
2025-07-08 11:51:57,755 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:51:57
2025-07-08 11:51:58,044 - src.data_providers.efinance_provider - INFO - 更新期货基础信息缓存
2025-07-08 11:51:59,857 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:52:00,102 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:52:00,103 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:52:00,103 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:52:00,107 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:52:00,123 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IC 具体月份合约
2025-07-08 11:52:00,127 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:52:00,130 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:52:00,132 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IM 具体月份合约
2025-07-08 11:52:00,139 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:52:00,142 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:52:00,143 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IF 具体月份合约
2025-07-08 11:52:00,146 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:52:00,154 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:52:00,155 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IH 具体月份合约
2025-07-08 11:52:00,158 - src.data_providers.efinance_provider - INFO - 总共获取到 16 个期货合约和 16 个行情
2025-07-08 11:52:00,158 - __main__ - INFO - 获取到 16 个期货合约和 16 个行情
2025-07-08 11:52:00,523 - src.data_providers.index_provider - INFO - efinance获取到 4 个指数行情
2025-07-08 11:52:00,738 - src.calculators.basis_calculator - INFO - 成功计算 16 个合约的基差
2025-07-08 11:52:00,894 - src.calculators.spread_calculator - INFO - 成功计算 12 个价差组合
2025-07-08 11:52:00,947 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:53:30,953 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:53:30
2025-07-08 11:53:30,954 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:53:31,290 - src.data_providers.efinance_provider - INFO - 成功获取 1000 个期货行情数据
2025-07-08 11:53:31,290 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:53:31,290 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:53:31,293 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:53:31,294 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IC 具体月份合约
2025-07-08 11:53:31,296 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:53:31,299 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:53:31,300 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IM 具体月份合约
2025-07-08 11:53:31,302 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:53:31,305 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:53:31,306 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IF 具体月份合约
2025-07-08 11:53:31,309 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:53:31,312 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:53:31,314 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IH 具体月份合约
2025-07-08 11:53:31,316 - src.data_providers.efinance_provider - INFO - 总共获取到 16 个期货合约和 16 个行情
2025-07-08 11:53:31,316 - __main__ - INFO - 获取到 16 个期货合约和 16 个行情
2025-07-08 11:53:31,478 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,526 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=11&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,595 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,613 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=11&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,697 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=11&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,712 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,809 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=11&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,822 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,891 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=11&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:31,908 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:53:32,002 - src.data_providers.index_provider - WARNING - 获取指数行情失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=6&pz=100&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 11:53:32,002 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-08 11:53:32,002 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-08 11:53:32,412 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-08 11:53:32,621 - src.calculators.basis_calculator - INFO - 成功计算 16 个合约的基差
2025-07-08 11:53:32,775 - src.calculators.spread_calculator - INFO - 成功计算 12 个价差组合
2025-07-08 11:53:32,814 - __main__ - INFO - 等待 90 秒后下次更新...
2025-07-08 11:55:02,820 - __main__ - INFO - 开始数据更新 - 2025-07-08 11:55:02
2025-07-08 11:55:02,821 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:55:02,934 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:03,057 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:03,140 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:03,250 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:03,360 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:03,458 - src.data_providers.efinance_provider - WARNING - 获取实时行情 失败 (尝试 1/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 11:55:03,459 - src.data_providers.efinance_provider - INFO - 等待 2.0 秒后重试...
2025-07-08 11:55:05,459 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:55:05,550 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:05,658 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:05,766 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:05,867 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:05,959 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:06,083 - src.data_providers.efinance_provider - WARNING - 获取实时行情 失败 (尝试 2/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 11:55:06,083 - src.data_providers.efinance_provider - INFO - 等待 4.0 秒后重试...
2025-07-08 11:55:10,085 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:55:10,176 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:10,303 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:10,397 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:10,516 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:10,616 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:10,727 - src.data_providers.efinance_provider - WARNING - 获取实时行情 失败 (尝试 3/4): HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 11:55:10,727 - src.data_providers.efinance_provider - INFO - 等待 8.0 秒后重试...
2025-07-08 11:55:18,727 - src.data_providers.efinance_provider - INFO - 正在获取实时行情数据...
2025-07-08 11:55:18,858 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:18,963 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:19,081 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:19,189 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:19,302 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:19,415 - src.data_providers.efinance_provider - ERROR - 获取实时行情 最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 11:55:19,416 - src.data_providers.efinance_provider - ERROR - 获取实时行情最终失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A113%2Cm%3A114%2Cm%3A115%2Cm%3A8%2Cm%3A142%2Cm%3A225&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 11:55:19,416 - src.data_providers.efinance_provider - WARNING - API调用失败，使用缓存数据
2025-07-08 11:55:19,416 - src.data_providers.efinance_provider - INFO - 获取到 1000 个期货行情数据
2025-07-08 11:55:19,416 - src.data_providers.efinance_provider - INFO - 处理品种: IC
2025-07-08 11:55:19,419 - src.data_providers.efinance_provider - INFO - 找到 10 个 IC 合约
2025-07-08 11:55:19,423 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IC 具体月份合约
2025-07-08 11:55:19,428 - src.data_providers.efinance_provider - INFO - 处理品种: IM
2025-07-08 11:55:19,435 - src.data_providers.efinance_provider - INFO - 找到 10 个 IM 合约
2025-07-08 11:55:19,437 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IM 具体月份合约
2025-07-08 11:55:19,440 - src.data_providers.efinance_provider - INFO - 处理品种: IF
2025-07-08 11:55:19,442 - src.data_providers.efinance_provider - INFO - 找到 10 个 IF 合约
2025-07-08 11:55:19,444 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IF 具体月份合约
2025-07-08 11:55:19,447 - src.data_providers.efinance_provider - INFO - 处理品种: IH
2025-07-08 11:55:19,451 - src.data_providers.efinance_provider - INFO - 找到 10 个 IH 合约
2025-07-08 11:55:19,452 - src.data_providers.efinance_provider - INFO - 过滤后找到 4 个 IH 具体月份合约
2025-07-08 11:55:19,455 - src.data_providers.efinance_provider - INFO - 总共获取到 16 个期货合约和 16 个行情
2025-07-08 11:55:19,455 - __main__ - INFO - 获取到 16 个期货合约和 16 个行情
2025-07-08 11:55:19,576 - urllib3.connectionpool - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:19,678 - urllib3.connectionpool - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:19,794 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:19,904 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:20,024 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))': /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297
2025-07-08 11:55:20,122 - src.data_providers.index_provider - WARNING - 获取指数行情失败: HTTPConnectionPool(host='push2.eastmoney.com', port=80): Max retries exceeded with url: /api/qt/clist/get?pn=1&pz=200&po=1&np=1&fltt=2&invt=2&fid=f12&fs=m%3A1+s%3A2%2Cm%3A0+t%3A5&fields=f12%2Cf14%2Cf3%2Cf2%2Cf15%2Cf16%2Cf17%2Cf4%2Cf8%2Cf10%2Cf9%2Cf5%2Cf6%2Cf18%2Cf20%2Cf21%2Cf13%2Cf124%2Cf297 (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')))
2025-07-08 11:55:20,122 - src.data_providers.index_provider - INFO - efinance获取到 0 个指数行情
2025-07-08 11:55:20,122 - src.data_providers.index_provider - INFO - 使用adata补充缺失的指数数据: ['sh000905', 'sh000852', 'sh000300', 'sh000016']
2025-07-08 11:55:20,529 - src.data_providers.index_provider - INFO - adata获取到 4 个指数行情
2025-07-08 11:55:20,732 - src.calculators.basis_calculator - INFO - 成功计算 16 个合约的基差
2025-07-08 11:55:20,882 - src.calculators.spread_calculator - INFO - 成功计算 12 个价差组合
2025-07-08 11:55:20,917 - __main__ - INFO - 等待 90 秒后下次更新...
