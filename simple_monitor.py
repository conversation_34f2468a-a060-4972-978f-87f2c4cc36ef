"""
简化版金融股指期货监控系统
基于已测试的功能，展示基差计算和成本矩阵
"""
import asyncio
import sys
import os
from datetime import datetime, date
import pandas as pd

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.data_models import (
    MonitoringConfig, FuturesContract, FuturesQuote, IndexQuote, BasisData
)
from src.data_providers.index_provider import IndexDataProvider
from src.utils.trading_calendar import TradingCalendar
from src.calculators.basis_calculator import BasisCalculator
from src.analyzers.cost_matrix import CostMatrixGenerator

# 模拟期货数据（只包含具体月份合约，过滤掉连续合约）
MOCK_FUTURES_DATA = {
    'IC': [
        {'code': 'IC2501', 'name': 'IC2501', 'price': 5865.4, 'volume': 11617, 'expiry': '2025-01-17'},
        {'code': 'IC2503', 'name': 'IC2503', 'price': 5868.2, 'volume': 8500, 'expiry': '2025-03-21'},
        {'code': 'IC2506', 'name': 'IC2506', 'price': 5870.1, 'volume': 6200, 'expiry': '2025-06-20'},
        {'code': 'IC2509', 'name': 'IC2509', 'price': 5871.0, 'volume': 4100, 'expiry': '2025-09-19'},
    ],
    'IM': [
        {'code': 'IM2501', 'name': 'IM2501', 'price': 6118.2, 'volume': 41209, 'expiry': '2025-01-17'},
        {'code': 'IM2503', 'name': 'IM2503', 'price': 6150.5, 'volume': 25000, 'expiry': '2025-03-21'},
        {'code': 'IM2506', 'name': 'IM2506', 'price': 6200.8, 'volume': 18000, 'expiry': '2025-06-20'},
        {'code': 'IM2509', 'name': 'IM2509', 'price': 6273.2, 'volume': 12000, 'expiry': '2025-09-19'},
    ],
    'IF': [
        {'code': 'IF2501', 'name': 'IF2501', 'price': 3962.0, 'volume': 25000, 'expiry': '2025-01-17'},
        {'code': 'IF2503', 'name': 'IF2503', 'price': 3968.5, 'volume': 18000, 'expiry': '2025-03-21'},
        {'code': 'IF2506', 'name': 'IF2506', 'price': 3972.2, 'volume': 12000, 'expiry': '2025-06-20'},
        {'code': 'IF2509', 'name': 'IF2509', 'price': 3975.0, 'volume': 8000, 'expiry': '2025-09-19'},
    ],
    'IH': [
        {'code': 'IH2501', 'name': 'IH2501', 'price': 2890.0, 'volume': 18000, 'expiry': '2025-01-17'},
        {'code': 'IH2503', 'name': 'IH2503', 'price': 2892.5, 'volume': 12000, 'expiry': '2025-03-21'},
        {'code': 'IH2506', 'name': 'IH2506', 'price': 2894.2, 'volume': 8000, 'expiry': '2025-06-20'},
        {'code': 'IH2509', 'name': 'IH2509', 'price': 2895.0, 'volume': 5000, 'expiry': '2025-09-19'},
    ]
}

class SimpleFuturesMonitor:
    """简化版期货监控系统"""
    
    def __init__(self):
        self.config = MonitoringConfig()
        self.index_provider = IndexDataProvider(use_adata_fallback=False)
        self.trading_calendar = TradingCalendar()
        self.basis_calculator = BasisCalculator(self.trading_calendar)
        self.cost_matrix_generator = CostMatrixGenerator()

        # 导入价差计算器
        from src.calculators.spread_calculator import SpreadCalculator
        self.spread_calculator = SpreadCalculator(self.trading_calendar)
        
    async def create_mock_contracts(self) -> list:
        """创建模拟期货合约"""
        contracts = []

        for product_code, futures_list in MOCK_FUTURES_DATA.items():
            # 计算品种总成交量
            total_volume = sum(f['volume'] for f in futures_list)

            for i, futures_data in enumerate(futures_list):
                # 计算成交量占比
                volume_ratio = (futures_data['volume'] / total_volume * 100) if total_volume > 0 else 0

                contract = FuturesContract(
                    contract_code=futures_data['code'],
                    product_code=product_code,
                    contract_name=futures_data['name'],
                    quote_id=f"8.{futures_data['code']}",
                    market_type='中金所',
                    expiry_date=datetime.strptime(futures_data['expiry'], '%Y-%m-%d').date(),
                    is_main_contract=(i == 0),  # 第一个为主力合约（成交量最大）
                    volume=futures_data['volume'],
                    volume_ratio=volume_ratio
                )
                contracts.append(contract)

        return contracts
    
    async def create_mock_futures_quotes(self, contracts: list) -> dict:
        """创建模拟期货行情"""
        quotes = {}
        
        for contract in contracts:
            # 从模拟数据中找到对应的价格
            for product_code, futures_list in MOCK_FUTURES_DATA.items():
                if contract.product_code == product_code:
                    for futures_data in futures_list:
                        if futures_data['code'] == contract.contract_code:
                            quote = FuturesQuote(
                                contract_code=contract.contract_code,
                                timestamp=datetime.now(),
                                current_price=futures_data['price'],
                                open_price=futures_data['price'] * 0.999,
                                high_price=futures_data['price'] * 1.002,
                                low_price=futures_data['price'] * 0.998,
                                volume=futures_data['volume'],
                                amount=futures_data['price'] * futures_data['volume'] * 200,  # 假设每手200
                                prev_close=futures_data['price'] * 0.995,
                                change_pct=0.5,
                                change_amount=futures_data['price'] * 0.005
                            )
                            quotes[contract.contract_code] = quote
                            break
        
        return quotes

    async def _create_mock_index_quotes(self) -> dict:
        """创建模拟指数行情（基于真实市场数据）"""
        # 使用近期真实的指数点位
        mock_index_data = {
            'sh000300': {'name': '沪深300', 'price': 3960.12, 'change_pct': -0.55},
            'sh000905': {'name': '中证500', 'price': 5865.40, 'change_pct': 0.24},
            'sh000852': {'name': '中证1000', 'price': 6118.20, 'change_pct': 0.45},
            'sh000016': {'name': '上证50', 'price': 2890.00, 'change_pct': -0.19},
        }

        quotes = {}
        for code, data in mock_index_data.items():
            quote = IndexQuote(
                index_code=code,
                index_name=data['name'],
                timestamp=datetime.now(),
                current_price=data['price'],
                open_price=data['price'] * 0.999,
                high_price=data['price'] * 1.002,
                low_price=data['price'] * 0.998,
                volume=100000000,  # 模拟成交量
                amount=data['price'] * 100000000,
                prev_close=data['price'] / (1 + data['change_pct']/100),
                change_pct=data['change_pct'],
                change_amount=data['price'] * data['change_pct']/100
            )
            quotes[code] = quote

        return quotes

    async def run_monitoring_cycle(self):
        """运行一次监控周期"""
        print("="*80)
        print(f"金融股指期货监控面板 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        try:
            # 1. 创建模拟合约和行情
            print("\n📊 获取期货数据...")
            contracts = await self.create_mock_contracts()
            futures_quotes = await self.create_mock_futures_quotes(contracts)
            print(f"✅ 获取到 {len(contracts)} 个期货合约，{len(futures_quotes)} 个行情")
            
            # 2. 获取指数行情（使用模拟数据，因为efinance指数数据有问题）
            print("\n📈 获取指数数据...")
            index_quotes = await self._create_mock_index_quotes()
            print(f"✅ 获取到 {len(index_quotes)} 个指数行情（模拟数据）")
            
            # 3. 计算基差
            print("\n🧮 计算基差成本...")
            basis_costs = await self.basis_calculator.calculate_multiple_basis(
                futures_quotes, index_quotes, contracts, self.config.futures_mapping
            )
            print(f"✅ 计算了 {len(basis_costs)} 个基差")
            
            # 4. 计算基差价差成本
            print("\n🔄 计算基差价差成本...")
            basis_spread_costs = await self.spread_calculator.calculate_basis_spread_costs(
                basis_costs, contracts
            )
            print(f"✅ 计算了 {len(basis_spread_costs)} 个合约的基差价差成本")

            # 5. 生成成本矩阵
            print("\n📋 生成成本矩阵...")
            cost_matrix = self.cost_matrix_generator.generate_cost_matrix(
                basis_costs, {}, contracts  # 暂时不计算价差
            )
            
            # 6. 显示结果
            await self._display_results(cost_matrix, contracts, index_quotes, basis_costs, basis_spread_costs)
            
        except Exception as e:
            print(f"❌ 监控周期执行失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def _display_results(self, cost_matrix, contracts, index_quotes, basis_costs, basis_spread_costs):
        """显示监控结果"""
        print("\n" + "="*80)
        print("📊 基差成本监控面板")
        print("="*80)

        if cost_matrix.basis_costs:
            # 创建基差矩阵DataFrame
            basis_df = self.cost_matrix_generator.create_basis_matrix_dataframe(
                cost_matrix.basis_costs, contracts
            )

            print("\n【综合成本矩阵】")
            comprehensive_df = self.cost_matrix_generator.create_comprehensive_cost_matrix(
                basis_costs, basis_spread_costs, contracts
            )
            if not comprehensive_df.empty:
                print(comprehensive_df.to_string(index=False))
            else:
                print("暂无综合成本数据")

            print("\n【基差成本矩阵】")
            print(basis_df.to_string(index=False))
            
            # 显示详细分析
            print("\n【详细分析】")
            for contract_code, basis_data in cost_matrix.basis_costs.items():
                contract = next((c for c in contracts if c.contract_code == contract_code), None)
                if contract:
                    print(f"\n🔸 {contract.product_code} ({contract.contract_name})")
                    print(f"   期货价格: {basis_data.futures_price:.2f}")
                    print(f"   现货价格: {basis_data.index_price:.2f}")
                    print(f"   基差: {basis_data.basis:.2f}")
                    print(f"   基差率: {basis_data.basis_rate:.2f}%")
                    print(f"   剩余天数: {basis_data.remaining_days}")
                    print(f"   年化成本: {basis_data.annualized_cost:.2f}%")
                    
                    # 基差分析
                    if basis_data.basis > 0:
                        print(f"   💡 期货升水 {basis_data.basis:.2f} 点")
                    else:
                        print(f"   💡 期货贴水 {abs(basis_data.basis):.2f} 点")
        
        # 显示指数信息
        print("\n【现货指数行情】")
        index_data = []
        for code, quote in index_quotes.items():
            index_data.append({
                '指数代码': code,
                '指数名称': quote.index_name,
                '最新价': quote.current_price,
                '涨跌幅(%)': quote.change_pct
            })
        
        if index_data:
            index_df = pd.DataFrame(index_data)
            print(index_df.to_string(index=False))
        
        # 汇总统计
        summary = self.cost_matrix_generator.generate_summary_statistics(cost_matrix)
        print(f"\n【汇总统计】")
        print(f"监控合约数量: {summary['overall_summary']['basis_opportunities']}")
        if summary['basis_summary']:
            print(f"平均基差率: {summary['basis_summary']['avg_basis_rate']:.2f}%")
            print(f"平均年化成本: {summary['basis_summary']['avg_annualized_cost']:.2f}%")
            print(f"最大年化成本: {summary['basis_summary']['max_annualized_cost']:.2f}%")
            print(f"最小年化成本: {summary['basis_summary']['min_annualized_cost']:.2f}%")
    
    async def start(self):
        """启动监控系统"""
        print("🚀 启动金融股指期货监控系统...")
        
        try:
            # 运行一次监控周期
            await self.run_monitoring_cycle()
            
            print(f"\n✅ 监控完成！")
            print(f"💡 提示：这是一个演示版本，使用了模拟的期货数据")
            print(f"💡 在生产环境中，期货数据将从efinance API实时获取")
            
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        await self.index_provider.close()
        await self.trading_calendar.close()

async def main():
    """主函数"""
    monitor = SimpleFuturesMonitor()
    await monitor.start()

if __name__ == "__main__":
    asyncio.run(main())
