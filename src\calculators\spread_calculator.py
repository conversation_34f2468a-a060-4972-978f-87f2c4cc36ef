"""
多合约价差分析模块
实现不同月份合约之间的价差计算和时间成本分析功能
"""
import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
import numpy as np
import pandas as pd
from itertools import combinations

from ..models.data_models import (
    FuturesQuote, FuturesContract, SpreadData
)
from ..utils.trading_calendar import TradingCalendar


class SpreadCalculator:
    """价差计算器"""
    
    def __init__(self, trading_calendar: TradingCalendar, trading_days_per_year: int = 243):
        self.trading_calendar = trading_calendar
        self.trading_days_per_year = trading_days_per_year
        self.logger = logging.getLogger(__name__)
    
    async def calculate_spread(self, near_quote: FuturesQuote, far_quote: FuturesQuote,
                             near_contract: FuturesContract, far_contract: FuturesContract) -> SpreadData:
        """计算两个合约之间的价差"""
        try:
            # 价差计算（远月 - 近月）
            spread = far_quote.current_price - near_quote.current_price
            
            # 价差率计算
            spread_rate = 0
            if near_quote.current_price != 0:
                spread_rate = (spread / near_quote.current_price) * 100
            
            # 计算时间差（交易日）
            time_diff_days = 0
            if near_contract.expiry_date and far_contract.expiry_date:
                time_diff_days = await self.trading_calendar.get_trading_days_between_contracts(
                    near_contract.expiry_date, far_contract.expiry_date
                )
            
            # 年化时间成本计算
            annualized_time_cost = await self._calculate_annualized_time_cost(
                spread_rate, time_diff_days
            )
            
            return SpreadData(
                near_contract=near_quote.contract_code,
                far_contract=far_quote.contract_code,
                timestamp=datetime.now(),
                near_price=near_quote.current_price,
                far_price=far_quote.current_price,
                spread=spread,
                spread_rate=spread_rate,
                time_diff_days=time_diff_days,
                annualized_time_cost=annualized_time_cost
            )
            
        except Exception as e:
            self.logger.error(f"计算价差失败: {e}")
            raise
    
    async def calculate_all_spreads(self, futures_quotes: Dict[str, FuturesQuote],
                                  contracts: List[FuturesContract]) -> Dict[str, SpreadData]:
        """计算所有可能的价差组合"""
        results = {}
        
        # 按品种分组合约
        product_contracts = self._group_contracts_by_product(contracts)
        
        for product_code, product_contracts_list in product_contracts.items():
            # 按到期日排序
            sorted_contracts = sorted(
                product_contracts_list, 
                key=lambda x: x.expiry_date or date.max
            )
            
            # 计算相邻合约间的价差
            for i in range(len(sorted_contracts) - 1):
                near_contract = sorted_contracts[i]
                far_contract = sorted_contracts[i + 1]
                
                # 检查是否有对应的行情数据
                if (near_contract.contract_code in futures_quotes and 
                    far_contract.contract_code in futures_quotes):
                    
                    try:
                        near_quote = futures_quotes[near_contract.contract_code]
                        far_quote = futures_quotes[far_contract.contract_code]
                        
                        spread_data = await self.calculate_spread(
                            near_quote, far_quote, near_contract, far_contract
                        )
                        
                        spread_key = f"{near_contract.contract_code}_{far_contract.contract_code}"
                        results[spread_key] = spread_data
                        
                    except Exception as e:
                        self.logger.error(f"计算价差失败 {near_contract.contract_code} vs {far_contract.contract_code}: {e}")
                        continue
        
        self.logger.info(f"成功计算 {len(results)} 个价差组合")
        return results
    
    async def calculate_calendar_spreads(self, futures_quotes: Dict[str, FuturesQuote],
                                       contracts: List[FuturesContract],
                                       max_months_apart: int = 3) -> Dict[str, SpreadData]:
        """计算日历价差（跨月价差）"""
        results = {}
        
        # 按品种分组
        product_contracts = self._group_contracts_by_product(contracts)
        
        for product_code, product_contracts_list in product_contracts.items():
            # 按到期日排序
            sorted_contracts = sorted(
                product_contracts_list,
                key=lambda x: x.expiry_date or date.max
            )
            
            # 计算不同月份间的价差
            for i in range(len(sorted_contracts)):
                for j in range(i + 1, min(i + max_months_apart + 1, len(sorted_contracts))):
                    near_contract = sorted_contracts[i]
                    far_contract = sorted_contracts[j]
                    
                    if (near_contract.contract_code in futures_quotes and 
                        far_contract.contract_code in futures_quotes):
                        
                        try:
                            near_quote = futures_quotes[near_contract.contract_code]
                            far_quote = futures_quotes[far_contract.contract_code]
                            
                            spread_data = await self.calculate_spread(
                                near_quote, far_quote, near_contract, far_contract
                            )
                            
                            spread_key = f"{near_contract.contract_code}_{far_contract.contract_code}"
                            results[spread_key] = spread_data
                            
                        except Exception as e:
                            self.logger.error(f"计算日历价差失败: {e}")
                            continue
        
        return results
    
    async def _calculate_annualized_time_cost(self, spread_rate: float, time_diff_days: int) -> float:
        """计算年化时间成本"""
        if time_diff_days <= 0:
            return 0
        
        # 年化时间成本 = (价差率 / 时间差天数) * 年交易天数
        annualized_cost = (spread_rate / time_diff_days) * self.trading_days_per_year
        return annualized_cost
    
    def _group_contracts_by_product(self, contracts: List[FuturesContract]) -> Dict[str, List[FuturesContract]]:
        """按品种分组合约"""
        product_contracts = {}
        for contract in contracts:
            if contract.product_code not in product_contracts:
                product_contracts[contract.product_code] = []
            product_contracts[contract.product_code].append(contract)
        return product_contracts
    
    def calculate_spread_statistics(self, spread_data_list: List[SpreadData]) -> Dict:
        """计算价差统计信息"""
        if not spread_data_list:
            return {}
        
        spreads = [data.spread for data in spread_data_list]
        spread_rates = [data.spread_rate for data in spread_data_list]
        time_costs = [data.annualized_time_cost for data in spread_data_list]
        time_diffs = [data.time_diff_days for data in spread_data_list]
        
        return {
            'count': len(spread_data_list),
            'spread_stats': {
                'mean': np.mean(spreads),
                'median': np.median(spreads),
                'std': np.std(spreads),
                'min': np.min(spreads),
                'max': np.max(spreads)
            },
            'spread_rate_stats': {
                'mean': np.mean(spread_rates),
                'median': np.median(spread_rates),
                'std': np.std(spread_rates),
                'min': np.min(spread_rates),
                'max': np.max(spread_rates)
            },
            'time_cost_stats': {
                'mean': np.mean(time_costs),
                'median': np.median(time_costs),
                'std': np.std(time_costs),
                'min': np.min(time_costs),
                'max': np.max(time_costs)
            },
            'time_diff_stats': {
                'mean': np.mean(time_diffs),
                'median': np.median(time_diffs),
                'min': np.min(time_diffs),
                'max': np.max(time_diffs)
            }
        }
    
    def identify_spread_opportunities(self, spread_data_list: List[SpreadData],
                                    normal_spread_range: Tuple[float, float] = (-0.5, 0.5)) -> List[Dict]:
        """识别价差交易机会"""
        opportunities = []
        min_normal, max_normal = normal_spread_range
        
        for data in spread_data_list:
            opportunity = None
            
            # 价差过小，可能的买入远月卖出近月机会
            if data.spread_rate < min_normal:
                opportunity = {
                    'type': 'buy_far_sell_near',
                    'spread_key': f"{data.near_contract}_{data.far_contract}",
                    'spread_rate': data.spread_rate,
                    'annualized_time_cost': data.annualized_time_cost,
                    'time_diff_days': data.time_diff_days,
                    'description': f'价差率 {data.spread_rate:.2f}% 过小，考虑买入远月卖出近月'
                }
            
            # 价差过大，可能的卖出远月买入近月机会
            elif data.spread_rate > max_normal:
                opportunity = {
                    'type': 'sell_far_buy_near',
                    'spread_key': f"{data.near_contract}_{data.far_contract}",
                    'spread_rate': data.spread_rate,
                    'annualized_time_cost': data.annualized_time_cost,
                    'time_diff_days': data.time_diff_days,
                    'description': f'价差率 {data.spread_rate:.2f}% 过大，考虑卖出远月买入近月'
                }
            
            if opportunity:
                opportunities.append(opportunity)
        
        return opportunities
    
    def analyze_contango_backwardation(self, spread_data_list: List[SpreadData]) -> Dict:
        """分析期货市场的升水贴水状态"""
        if not spread_data_list:
            return {}
        
        contango_count = 0  # 升水（远月高于近月）
        backwardation_count = 0  # 贴水（远月低于近月）
        
        contango_spreads = []
        backwardation_spreads = []
        
        for data in spread_data_list:
            if data.spread > 0:
                contango_count += 1
                contango_spreads.append(data.spread_rate)
            else:
                backwardation_count += 1
                backwardation_spreads.append(data.spread_rate)
        
        total_count = len(spread_data_list)
        
        analysis = {
            'total_spreads': total_count,
            'contango_count': contango_count,
            'backwardation_count': backwardation_count,
            'contango_percentage': (contango_count / total_count) * 100 if total_count > 0 else 0,
            'backwardation_percentage': (backwardation_count / total_count) * 100 if total_count > 0 else 0,
        }
        
        if contango_spreads:
            analysis['contango_stats'] = {
                'mean_spread_rate': np.mean(contango_spreads),
                'max_spread_rate': np.max(contango_spreads),
                'min_spread_rate': np.min(contango_spreads)
            }
        
        if backwardation_spreads:
            analysis['backwardation_stats'] = {
                'mean_spread_rate': np.mean(backwardation_spreads),
                'max_spread_rate': np.max(backwardation_spreads),
                'min_spread_rate': np.min(backwardation_spreads)
            }
        
        # 市场状态判断
        if contango_count > backwardation_count * 2:
            analysis['market_state'] = 'strong_contango'
            analysis['market_description'] = '市场处于强升水状态，远月合约普遍高于近月'
        elif backwardation_count > contango_count * 2:
            analysis['market_state'] = 'strong_backwardation'
            analysis['market_description'] = '市场处于强贴水状态，远月合约普遍低于近月'
        else:
            analysis['market_state'] = 'mixed'
            analysis['market_description'] = '市场升贴水状态混合，需要具体分析'
        
        return analysis
    
    def create_spread_report(self, spread_data_list: List[SpreadData]) -> Dict:
        """生成价差分析报告"""
        if not spread_data_list:
            return {'error': '无价差数据'}
        
        # 基础统计
        stats = self.calculate_spread_statistics(spread_data_list)
        
        # 交易机会
        opportunities = self.identify_spread_opportunities(spread_data_list)
        
        # 升贴水分析
        contango_analysis = self.analyze_contango_backwardation(spread_data_list)
        
        # 按品种分组分析
        by_product = {}
        for data in spread_data_list:
            # 从合约代码提取品种
            product = data.near_contract[:2]
            if product not in by_product:
                by_product[product] = []
            by_product[product].append(data)
        
        # 生成报告
        report = {
            'timestamp': datetime.now(),
            'summary': {
                'total_spreads': len(spread_data_list),
                'products_count': len(by_product),
                'opportunities_count': len(opportunities)
            },
            'statistics': stats,
            'opportunities': opportunities,
            'contango_analysis': contango_analysis,
            'by_product': {}
        }
        
        # 按品种统计
        for product, product_data in by_product.items():
            product_stats = self.calculate_spread_statistics(product_data)
            product_contango = self.analyze_contango_backwardation(product_data)
            
            report['by_product'][product] = {
                'spreads_count': len(product_data),
                'statistics': product_stats,
                'contango_analysis': product_contango,
                'spreads': [
                    {
                        'near_contract': data.near_contract,
                        'far_contract': data.far_contract,
                        'spread': data.spread,
                        'spread_rate': data.spread_rate,
                        'annualized_time_cost': data.annualized_time_cost,
                        'time_diff_days': data.time_diff_days
                    }
                    for data in product_data
                ]
            }
        
        return report
